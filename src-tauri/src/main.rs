// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::path::PathBuf;
use tauri::Manager;
use image::{ImageFormat, DynamicImage};
use std::io::Cursor;
use base64::{Engine as _, engine::general_purpose};

// Проверяем, является ли файл изображением
#[tauri::command]
async fn is_image_file(file_path: String) -> Result<bool, String> {
    let path = PathBuf::from(&file_path);

    if !path.exists() {
        return Ok(false);
    }

    // Проверяем расширение файла
    if let Some(extension) = path.extension() {
        let ext = extension.to_string_lossy().to_lowercase();
        match ext.as_str() {
            "jpg" | "jpeg" | "png" | "gif" | "bmp" | "tiff" | "webp" => Ok(true),
            _ => Ok(false),
        }
    } else {
        Ok(false)
    }
}

// Получаем предпросмотр изображения в формате base64
#[tauri::command]
async fn get_image_preview(file_path: String) -> Result<String, String> {
    let path = PathBuf::from(&file_path);

    if !path.exists() {
        return Err("Файл не найден".into());
    }

    let img = match image::open(&path) {
        Ok(img) => img,
        Err(e) => return Err(format!("Не удалось открыть изображение: {}", e)),
    };

    // Создаем миниатюру 100x100
    let thumbnail = img.thumbnail(100, 100);

    // Конвертируем в JPEG и кодируем в base64
    let mut buffer = Vec::new();
    let mut cursor = Cursor::new(&mut buffer);

    match thumbnail.write_to(&mut cursor, ImageFormat::Jpeg) {
        Ok(_) => {
            let base64_string = general_purpose::STANDARD.encode(&buffer);
            Ok(format!("data:image/jpeg;base64,{}", base64_string))
        },
        Err(e) => Err(format!("Не удалось создать предпросмотр: {}", e)),
    }
}

// Конвертируем изображение в JPEG
#[tauri::command]
async fn convert_to_jpeg(_app: tauri::AppHandle, file_path: String) -> Result<String, String> {
    let path = PathBuf::from(&file_path);

    if !path.exists() {
        return Err("Файл не найден".into());
    }

    let img = match image::open(&path) {
        Ok(img) => img,
        Err(e) => return Err(format!("Не удалось открыть изображение: {}", e)),
    };

    let mut new_path = path.clone();
    new_path.set_extension("jpeg");
    let new_file_name = new_path.file_name().unwrap().to_str().unwrap().to_string();

    // Конвертируем в RGB если нужно (для JPEG)
    let rgb_img = img.to_rgb8();
    let dynamic_img = DynamicImage::ImageRgb8(rgb_img);

    match dynamic_img.save_with_format(&new_path, ImageFormat::Jpeg) {
        Ok(_) => Ok(new_file_name),
        Err(e) => Err(format!("Не удалось сохранить JPEG: {}", e)),
    }
}

fn main() {
    tauri::Builder::default()
        .setup(|app| {
            // Получаем главное окно
            let window = app.get_webview_window("main").unwrap();

            // Применяем специфичные для macOS настройки для красивой прозрачности
            #[cfg(target_os = "macos")]
            {
                use tauri::TitleBarStyle;
                window.set_title_bar_style(TitleBarStyle::Transparent).ok();
            }

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            convert_to_jpeg,
            is_image_file,
            get_image_preview
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}