// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::path::PathBuf;
use tauri::Manager;

// Создаем команду, которая будет доступна из фронтенда
#[tauri::command]
async fn convert_to_jpeg(_app: tauri::AppHandle, file_path: String) -> Result<String, String> {
    let path = PathBuf::from(&file_path);

    if !path.exists() {
        return Err("Файл не найден".into());
    }

    let img = match image::open(&path) {
        Ok(img) => img,
        Err(e) => return Err(format!("Не удалось открыть изображение: {}", e)),
    };

    let mut new_path = path.clone();
    new_path.set_extension("jpeg");
    let new_file_name = new_path.file_name().unwrap().to_str().unwrap().to_string();

    match img.save(&new_path) {
        Ok(_) => Ok(new_file_name),
        Err(e) => Err(format!("Не удалось сохранить JPEG: {}", e)),
    }
}

fn main() {
    tauri::Builder::default()
        .setup(|app| {
            // ИСПРАВЛЕНИЕ: Используем get_webview_window напрямую, как советует компилятор
            let window = app.get_webview_window("main").unwrap();

            // Применяем специфичные для macOS настройки для красивой прозрачности
            // #[cfg(target_os = "macos")]
            // window.set_vibrancy(Some(tauri::macos::Vibrancy::AppearanceBased))
            //     .expect("Unsupported platform! 'apply_vibrancy' is only supported on macOS");

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![convert_to_jpeg])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}