:root {
  --background-color: rgba(20, 22, 25, 0.8);
  --border-color: rgba(255, 255, 255, 0.1);
  --text-color: #e0e0e0;
  --icon-color: #5f6368;
}

body {
  background-color: transparent;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--text-color);
}

.container {
  background-color: var(--background-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 12px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  user-select: none;
}

.drop-icon {
    width: 80px;
    height: 80px;
    background-color: var(--icon-color);
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>');
    mask-size: contain;
    mask-repeat: no-repeat;
    mask-position: center;
}

.empty-state p {
    margin-top: 16px;
    font-size: 16px;
    color: var(--text-color);
}

/* File List */
.file-list-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.file-list {
    flex-grow: 1;
    overflow-y: auto;
    padding: 16px;
}

.file-item {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.file-item .preview {
    width: 50px;
    height: 50px;
    background-color: var(--icon-color);
    border-radius: 4px;
    margin-right: 12px;
    /* Иконка файла */
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>');
    mask-size: 60%;
    mask-repeat: no-repeat;
    mask-position: center;
}

/* Статусы */
.file-item.status-done .preview {
  mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="green" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>');
}

.file-item .info {
    flex-grow: 1;
}

.file-item .name {
    font-weight: 500;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

 .file-item .status {
    font-size: 12px;
    color: #a0a0a0;
    margin: 4px 0 0 0;
}

.file-item .actions {
    display: flex;
    align-items: center;
}

button {
    background-color: #333;
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
}

button:hover {
    background-color: #444;
}

.remove-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #888;
    cursor: pointer;
    margin-left: 10px;
}

/* Панель управления */
.controls {
    padding: 16px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}