import { useState, useEffect } from 'react';
import { listen } from '@tauri-apps/api/event';
import { core } from '@tauri-apps/api';
const { invoke } = core;
import './App.css';

interface FileItem {
    id: number;
    path: string;
    name: string;
    status: 'pending' | 'converting' | 'done' | 'error';
    error?: string;
}

function App() {
    const [files, setFiles] = useState<FileItem[]>([]);

    useEffect(() => {
        const unlisten = listen<string[]>('tauri://file-drop', (event) => {
            const newFiles = event.payload.map((path, index) => {
                const name = path.split(/[\\/]/).pop() || 'unknown';
                return { id: Date.now() + index, path, name, status: 'pending' } as FileItem;
            });
            setFiles(prev => [...prev, ...newFiles]);
        });

        return () => {
            unlisten.then(f => f());
        };
    }, []);

    const handleConvertOne = async (id: number) => {
        setFiles(files => files.map(f => f.id === id ? { ...f, status: 'converting' } : f));
        const file = files.find(f => f.id === id);
        if (file) {
            try {
                const newName: string = await invoke('convert_to_jpeg', { filePath: file.path });
                setFiles(files => files.map(f =>
                    f.id === id ? { ...f, status: 'done', name: newName } : f
                ));
            } catch (error) {
                 setFiles(files => files.map(f =>
                    f.id === id ? { ...f, status: 'error', error: String(error) } : f
                ));
            }
        }
    };

    const handleConvertAll = () => {
        files.filter(f => f.status === 'pending').forEach(f => handleConvertOne(f.id));
    };

    const removeFile = (id: number) => {
        setFiles(files => files.filter(f => f.id !== id));
    };

    const clearAll = () => {
        setFiles([]);
    };

    return (
        <div className="container">
            {files.length === 0 ? (
                <div className="empty-state">
                    <div className="drop-icon"></div>
                    <p>Перетащите файлы для конвертации</p>
                </div>
            ) : (
                <div className="file-list-container">
                   <div className="file-list">
                       {files.map(file => (
                           <div key={file.id} className={`file-item status-${file.status}`}>
                               <div className="preview"></div>
                               <div className="info">
                                   <p className="name">{file.name}</p>
                                   <p className="status">{file.status === 'done' ? 'Завершено' : file.status === 'error' ? `Ошибка: ${file.error}` : 'Ожидание'}</p>
                               </div>
                               <div className="actions">
                                   {file.status === 'pending' && <button onClick={() => handleConvertOne(file.id)}>Конвертировать</button>}
                                   <button className="remove-btn" onClick={() => removeFile(file.id)}>×</button>
                               </div>
                           </div>
                       ))}
                   </div>
                    <div className="controls">
                        <button onClick={handleConvertAll}>Конвертировать все</button>
                        <button onClick={clearAll}>Очистить все</button>
                    </div>
                </div>
            )}
        </div>
    );
}

export default App;